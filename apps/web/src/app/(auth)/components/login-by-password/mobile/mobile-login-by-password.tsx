import { AuthTabbar } from "@/app/(auth)/components/auth-tabbar";
import { CaptchaInput } from "@/components/captcha-input";
import { CaptchaTypeEnum } from "@/components/captcha-input/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputPassword } from "@workspace/ui/components/input-password";
import { Loading } from "@workspace/ui/components/loading";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  useCaptchaRequestTextCaptcha,
  useCaptchaWhichCaptchaIsActive,
} from "@workspace/investment-api/auto-generated/apis/captcha/captcha";
import { SplashLoading } from "@/components/splash-loading";
import { CaptchaInputRef } from "@/components/captcha-input/captcha-input";
import { Input } from "@workspace/ui/components/input";
import { useLoginQueryStates } from "@/app/(auth)/hooks/useLoginQueryStates";
import { toast } from "@workspace/ui/components/toast";

function checkIranianNationalId(input: string): boolean {
  if (!/^\d{10}$/.test(input)) return false;
  const check = parseInt(input[9]!, 10);
  const sum =
    input
      .split("")
      .slice(0, 9)
      .reduce((acc, digit, i) => acc + parseInt(digit, 10) * (10 - i), 0) % 11;
  return (sum < 2 && check === sum) || (sum >= 2 && check + sum === 11);
}

const formSchema = z.object({
  nationalCodeOrMobileNumber: z
    .string()
    .nonempty("لطفا ابتدا فرم زیر را تکمیل کنید.")
    .refine(
      (value) => {
        const isPhone = /^09\d{9}$/.test(value);
        const isNationalCode =
          /^\d{10}$/.test(value) && checkIranianNationalId(value);
        return isPhone || isNationalCode;
      },
      {
        message: "اطلاعات وارد شده صحیح نیست!",
      },
    ),
  password: z.string().nonempty("لطفا ابتدا فرم زیر را تکمیل کنید."),
  secretKey: z.string(),
  captchaCode: z.string(),
});

export type LoginByPasswordFormValues = z.infer<typeof formSchema>;

export type LoginByPasswordHandleSubmitFunction = (
  values: LoginByPasswordFormValues,
) => Promise<unknown>;

interface LoginByPasswordProps {
  handleSubmit: LoginByPasswordHandleSubmitFunction;
  secretKey?: string;
  mobile?: string;
}

function MobileLoginByPassword({ handleSubmit }: LoginByPasswordProps) {
  const [captchaType, setCaptchaType] = useState<null | number>(null);
  const [queryStates, setQueryStates] = useLoginQueryStates();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      nationalCodeOrMobileNumber: "",
      password: "",
      secretKey: "",
      captchaCode: "",
    },
    mode: "onSubmit",
    context: { captchaType }, 
  });

  const inputRef = useRef<HTMLInputElement>(null);

  const captchaInputRef = useRef<CaptchaInputRef>(null);

  const secretKey = form.watch("secretKey");
  const captchaCode = form.watch("captchaCode");

  const [isLoading, setIsLoading] = useState(
    captchaType === CaptchaTypeEnum.GoogleRecaptchaV3 ? !secretKey : true,
  );

  const validateCaptchaCode = (value: string) => {
    if (captchaType === CaptchaTypeEnum.GoogleRecaptchaV3) {
      return true; 
    }

    if (!value || value.trim().length === 0) {
      return "کد تصویری منقضی شده یا صحیح نیست!";
    }

    return true;
  };

  useEffect(() => {
    if (captchaType !== null) {
      form.trigger("captchaCode");
    }
  }, [captchaType, form]);

  useEffect(() => {
    const errors = form.formState.errors;
    const errorMessages = Object.values(errors)
      .map((error) => error?.message)
      .filter(Boolean);

    if (!!errorMessages.length) {
      toast.error(errorMessages[0]);
    }
  }, [form.formState.errors]);

  function onSubmit(values: z.infer<typeof formSchema>) {
    inputRef.current?.blur();
    captchaInputRef.current?.refreshCaptcha();

    return handleSubmit({ ...values, captchaCode, secretKey: secretKey || "" });
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="mt-13 flex flex-col gap-3.5">
        <h2 className="text-text-nautral-default text-lg font-bold">
          خوش آمدید.
        </h2>
        <AuthTabbar />
      </div>

      <div className="mt-6 flex flex-1 flex-col justify-between">
        <div className="flex flex-col justify-between gap-6">
          <Form {...form}>
            <form
              id="login-form"
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex flex-col justify-between"
            >
              {isLoading && <SplashLoading />}

              <div className="flex flex-col gap-3">
                <FormField
                  control={form.control}
                  name="nationalCodeOrMobileNumber"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          size="lg"
                          title="شماره موبایل/کد ملی"
                          {...field}
                          ref={inputRef}
                          aria-invalid={!!fieldState?.error?.message}
                          data-test="dbb97343-4f3a-4c52-8327-5012050dc333"
                          onBlur={() => {
                            field.onBlur();
                            form.clearErrors("nationalCodeOrMobileNumber");
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="flex flex-col gap-2">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormControl>
                          <InputPassword
                            size="lg"
                            title="رمز عبور"
                            {...field}
                            ref={inputRef}
                            aria-invalid={!!fieldState?.error?.message}
                            data-test="0aa51f21-0928-4e84-9527-0f0ea424e625"
                            onBlur={() => {
                              field.onBlur();
                              form.clearErrors("password");
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <Link href="#" className="text-text-primary-default text-xs">
                    بازیابی رمز عبور
                  </Link>
                </div>

                <FormField
                  control={form.control}
                  name="captchaCode"
                  rules={{
                    validate: validateCaptchaCode,
                  }}
                  render={({ field, fieldState }) => (
                    <FormItem className="mt-3">
                      <FormControl>
                        <CaptchaInput
                          useCaptchaWhichCaptchaIsActive={
                            useCaptchaWhichCaptchaIsActive
                          }
                          useCaptchaRequestTextCaptcha={
                            useCaptchaRequestTextCaptcha
                          }
                          onCaptchaChange={({ secretKey, captchaCode }) => {
                            field.onChange(captchaCode);
                            form.setValue("secretKey", secretKey || "");
                          }}
                          googleRecaptchaSiteKey={
                            process.env.NEXT_PUBLIC_RECAPTCHA || ""
                          }
                          inputProps={{
                            title: "کد  را وارد کنید",
                            placeholder: " ",
                            // helperText: fieldState?.error?.message,
                            "aria-invalid": !!fieldState?.error?.message,
                          }}
                          setIsLoading={setIsLoading}
                          ref={captchaInputRef}
                          setCaptchaType={setCaptchaType}
                          error={form?.formState?.errors?.captchaCode?.message}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </form>
          </Form>
        </div>

        <div className="flex flex-col gap-3">
          <Button
            variant="noFrame"
            color="default"
            size="md"
            type="button"
            form="login-form"
            disabled={form.formState.isSubmitting}
            data-test="8c97c56e-fa65-4aff-83ed-0ccfed41778e"
            onClick={() => setQueryStates({ loginType: 200 })}
          >
            ورود با رمز یک‌بارمصرف
          </Button>
          <Button
            variant="fill"
            size="md"
            type="submit"
            form="login-form"
            disabled={form.formState.isSubmitting}
            endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
            data-test="8c97c56e-fa65-4aff-83ed-0ccfed41778e"
          >
            ورود
          </Button>
        </div>
      </div>
    </div>
  );
}
export default MobileLoginByPassword;
