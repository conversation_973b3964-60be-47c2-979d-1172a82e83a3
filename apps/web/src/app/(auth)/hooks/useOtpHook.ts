import { useLoginQueryStates } from "@/app/(auth)/hooks/useLoginQueryStates";
import { THREE_TIMES_TRY_ERROR, WRONG_CAPTCHA_CODE } from "@/app/(auth)/utils";
import { ROUTE_LOGIN } from "@/constants/routes";
import { useAuthSendLoginOTP } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { ApiResultOfGetOtpResponse } from "@workspace/investment-api/auto-generated/models";
import { toast } from "@workspace/ui/components/toast";
import { AxiosError } from "axios";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface IOtpHookProps {
  secretKey?: string;
  setSecretKey?: (val: string) => void;
}

export type OnSendOtpFunc = {
  sk?: string;
  enabledLockedError?: boolean;
};

export type OTPHookReturnType = {
  onSendOtp: (params: OnSendOtpFunc) => Promise<ApiResultOfGetOtpResponse>;
  isLoadingSendOtp: boolean;
  otpSendDate: string;
  otpExpirationDate: string;
};

export const useOtpHook = (): OTPHookReturnType => {
  const router = useRouter();
  const [otpSendDate, setOtpSendDate] = useState<string>("");
  const [otpExpirationDate, setOtpExpirationDate] = useState<string>("");
  const errorTile = (errorCode?: string) =>
    errorCode && THREE_TIMES_TRY_ERROR.includes(errorCode)
      ? "حساب شما مسدود است!"
      : " ";

  const [queryStates, setQueryStates] = useLoginQueryStates();

  const loginMethodHeaders = {
    "Content-Type": "multipart/form-data",
  };

  const { mutateAsync: sendLoginOtpMutate, isPending: isLoadingSendOtp } =
    useAuthSendLoginOTP({
      request: {
        headers: loginMethodHeaders,
      },
      mutation: { retry: 0 },
    });

  const onSendOtp = async ({ sk }: OnSendOtpFunc) => {
    return await sendLoginOtpMutate(
      { data: { secretKey: sk } },
      {
        onSuccess: (data) => {
          if (data?.data?.otpSendDate) setOtpSendDate(data?.data?.otpSendDate);
          if (data?.data?.otpExpirationDate)
            setOtpExpirationDate(data?.data?.otpExpirationDate);
        },
        onError: (error: AxiosError<{ errorMessage?: string; errorCode?: string }>) => {
          const errorData = error.response?.data;
          if (errorData?.errorMessage) {
            const hasErrorTitle = errorTile(errorData.errorCode);
            toast.error(
              hasErrorTitle
                ? errorTile(errorData.errorCode)
                : errorData.errorMessage,
              {
                description: hasErrorTitle
                  ? errorData.errorMessage
                  : undefined,
              },
            );
          }
        },
      },
    );
  };

  return { onSendOtp, isLoadingSendOtp, otpSendDate, otpExpirationDate };
};
